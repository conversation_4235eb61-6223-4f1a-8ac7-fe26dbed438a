<template>
  <div class="equipmentlibrary auto-fill">
    <ui-switch-tab
      class="ui-switch-tab mb-sm"
      v-model="state"
      :tab-list="stateOptions"
      @beforeChangeTab="beforeChangeTab"
    >
    </ui-switch-tab>
    <search-card :optionList="getOptionList" @startSearch="startSearch" :checkList="checkList"></search-card>
    <TableCard ref="infoCard" class="card-list auto-fill keypersonlibrary" :loadData="loadDataCard">
      <template #card="{ row }">
        <InfoCard
          class="card1"
          :checkNum="1"
          :list="row"
          :cardInfo="faceCardInfo"
          @bigImageUrl="bigImageUrl"
          v-if="state === 'face'"
        >
        </InfoCard>
        <UiGatherCard v-else class="card" :list="row" :cardInfo="carCardInfo" @bigImageUrl="bigImageUrl"></UiGatherCard>
      </template>
    </TableCard>

    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'exceptionlibrary',
  props: {},
  data() {
    return {
      optionList: {},
      faceCardInfo: [
        { icon: 'icon-shijian', value: 'shotTime', text: 'tip' },
        { icon: 'icon-dizhi', value: 'address', text: 'tip' },
        { icon: '', value: 'resultTip', text: 'tip' },
      ],
      carCardInfo: [
        { name: '车牌号', value: 'plateNo' },
        { name: '车牌颜色', value: 'plateColor', algorithm: 'colorType' },
        { name: '抓拍时间', value: 'shotTime' },
        { name: '抓拍地点', value: 'address' },
        { name: '异常原因', value: 'reason', algorithm: 'colorType' },
      ],
      loadDataCard: (parameter) => {
        return this.$http
          .post(
            evaluationoverview[this.state === 'face' ? 'getFaceDetailData' : 'getVehicleDetailData'],
            Object.assign(parameter, this.searchData),
          )
          .then((res) => {
            return res.data;
          });
      },
      imgList: [], // 大图图片
      bigPictureShow: false, //大图展示
      checkPicture: false, //查看图片
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      state: 'face',
      stateOptions: [
        {
          label: '人脸视图数据指标',
          value: 'face',
        },
        {
          label: '车辆视图数据指标',
          value: 'vehicle',
        },
      ],
      searchData: {},
      checkList: [],
    };
  },
  created() {
    this.getUsableIndexList();
    this.getUrlIndexErrorCode();
  },
  methods: {
    startSearch(searchData) {
      console.log('searchData', searchData);
      this.searchData = searchData;
      this.$refs.infoCard.info(true);
    },
    async getUsableIndexList() {
      try {
        let {
          data: { data },
        } = await this.$http.get(evaluationoverview.getUsableIndexList);
        this.optionList = data.data || {};
      } catch (e) {
        console.log(e);
      }
    },
    async getUrlIndexErrorCode() {
      try {
        let {
          data: { data },
        } = await this.$http.get(evaluationoverview.getUrlIndexErrorCode);
        this.checkList = data || [];
      } catch (error) {
        console.log(error);
      }
    },
    beforeChangeTab(val) {
      console.log('val', val);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    bigImageUrl(item) {
      if (!item) {
        this.$Message.warning('大图图片地址缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
  },
  watch: {},
  computed: {
    getOptionList() {
      return this.optionList[this.state] || [];
    },
    getCardInfo() {
      return this.state === 'face' ? this.CarCardInfo : this.CarCardInfo;
    },
  },
  components: {
    UiSwitchTab: require('@/components/ui-switch-tab/ui-switch-tab.vue').default,
    SearchCard: require('@/views/viewassets/videopictureassets/exceptionlibrary/searchCard.vue').default,
    TableCard: require('@/views/governanceevaluation/evaluationoverview/components/face/components/tableCard.vue')
      .default,
    LookScene: require('@/components/look-scene').default,
    InfoCard: require('@/views/governanceevaluation/evaluationoverview/components/face/components/infoCard.vue')
      .default,
    UiGatherCard: require('@/views/governanceevaluation/evaluationoverview/components/car/component/ui-gather-card.vue')
      .default,
  },
};
</script>
<style lang="less" scoped>
.equipmentlibrary {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);
  padding: 20px 20px 0;
  .keypersonlibrary {
    max-height: 100% !important;
  }
}
.card-list {
  width: 100%;
}
.card {
  width: calc(calc(100% - 40px) / 4) !important;
  margin: 0 5px 10px !important;
  padding-bottom: 8px !important;
  height: max-content !important;
}
</style>
