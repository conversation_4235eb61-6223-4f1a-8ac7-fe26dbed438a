<template>
  <div class="place-management height-full">
    <div :class="[loading ? 'mask' : '', 'map-box']">
      <map-base
        ref="deviceMap"
        class="map-base"
        :camera-list="cameraList"
        :map-position="mapPosition"
        :choose-map-item="chooseMapItem"
        :need-query-device-info="true"
        :right-button-group="true"
        :geo-regions="geoRegions"
        :zoom-change="zoomChange"
        :drag-end="dragEnd"
        :center="center"
        @getExtent="getExtent"
        @queryDeviceInfo="queryDeviceInfo"
      ></map-base>
    </div>
    <div class="map-search" v-show="!mapOrgTreeVisible" @click="openMapOrgTree">
      <div>
        <i class="icon-font icon-sousuo"></i>
      </div>
    </div>

    <transition name="draw">
      <!--这里的name 和 css 类名第一个字段要一样-->
      <div class="place-box height-full auto-fill" v-show="mapOrgTreeVisible">
        <map-search-tree
          ref="uiSearchTree"
          placeholder="请输入组织机构名称或组织机构编码"
          :scroll="360"
          :check-strictly="true"
          :show-checkbox="false"
          :default-props="defaultProps"
          :node-key="nodeKey"
          @onDeviceListChange="onDeviceListChange"
          @selectDevice="selectDevice"
          @putaway="mapOrgTreeVisible = false"
          @selectOrg="selectOrg"
        >
        </map-search-tree>
      </div>
    </transition>

    <div class="layer-management">
      <el-popover
        popper-class="layer-popover"
        visible-arrow="false"
        placement="bottom-end"
        v-model="layerVisible"
        trigger="click"
      >
        <div class="layer-title">图层管理</div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-equipmentlibrary"></i>
              </span>
              <span>视频监控</span>
              <span class="count">
<!--                (<span>{{ sbgnlxStatistic.length ? sbgnlxStatistic.find((e) => e.sbgnlx === '1').count : 0 }}</span
                >)-->
              </span>
            </div>
            <i-switch size="small" value="1" true-value="1" @on-change="(e) => layerCondition(e, 'sbgnlx', '1')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-renliankakou"></i>
              </span>
              <span>人脸卡口</span>
              <span class="count">
<!--                (<span>{{ sbgnlxStatistic.length ? sbgnlxStatistic.find((e) => e.sbgnlx === '3').count : 0 }}</span
                >)-->
              </span>
            </div>
            <i-switch size="small" value="3" true-value="3" @on-change="(e) => layerCondition(e, 'sbgnlx', '3')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-cheliangshitushuju"></i>
              </span>
              <span>车辆卡口</span>
              <span class="count">
<!--                (<span>{{ sbgnlxStatistic.length ? sbgnlxStatistic.find((e) => e.sbgnlx === '2').count : 0 }}</span
                >)-->
              </span>
            </div>
            <i-switch size="small" value="2" true-value="2" @on-change="(e) => layerCondition(e, 'sbgnlx', '2')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-qitagongju"></i>
              </span>
              <span>其他</span>
              <span class="count">
<!--                (<span>{{ sbgnlxStatistic.length ? sbgnlxStatistic.find((e) => e.sbgnlx === '2').count : 0 }}</span
                >)-->
              </span>
            </div>
            <i-switch size="small" value="" true-value="" @on-change="(e) => layerCondition(e, 'sbgnlx', '')" />
          </div>
        </div>
        <Divider></Divider>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-yileidian"></i>
              </span>
              <span>一类点</span>
            </div>
            <i-switch size="small" value="1" true-value="1" @on-change="(e) => layerCondition(e, 'sbdwlx', '1')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-ersanleidian"></i>
              </span>
              <span>二三类点</span>
            </div>
            <i-switch size="small" value="2,3" true-value="2,3" @on-change="(e) => layerCondition(e, 'sbdwlx', '2,3')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-gonganneibujiankongdian"></i>
              </span>
              <span>公安内部监控点</span>
            </div>
            <i-switch size="small" value="4" true-value="4" @on-change="(e) => layerCondition(e, 'sbdwlx', '4')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-qitagongju"></i>
              </span>
              <span>其他</span>
              <span class="count">
<!--                (<span>{{ sbgnlxStatistic.length ? sbgnlxStatistic.find((e) => e.sbgnlx === '2').count : 0 }}</span
                >)-->
              </span>
            </div>
            <i-switch size="small" value="" true-value="" @on-change="(e) => layerCondition(e, 'sbdwlx', '')" />
          </div>
        </div>
        <Divider></Divider>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-jiancehegedianwei"></i>
              </span>
              <span>检测合格点位</span>
            </div>
            <i-switch
              size="small"
              value="0000"
              true-value="0000"
              @on-change="(e) => layerCondition(e, 'checkStatus', '0000')"
            />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-jianceyichangdianwei"></i>
              </span>
              <span>检测异常点位</span>
            </div>
            <i-switch
              size="small"
              value="0100,0001,0010,0011,0101,0110,0111"
              true-value="0100,0001,0010,0011,0101,0110,0111"
              @on-change="(e) => layerCondition(e, 'checkStatus', '0100,0001,0010,0011,0101,0110,0111')"
            />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-qitagongju"></i>
              </span>
              <span>未检测</span>
              <span class="count">
<!--                (<span>{{ sbgnlxStatistic.length ? sbgnlxStatistic.find((e) => e.sbgnlx === '2').count : 0 }}</span
                >)-->
              </span>
            </div>
            <i-switch size="small" value="" true-value="" @on-change="(e) => layerCondition(e, 'checkStatus', '1000')" />
          </div>
        </div>
        <div class="form-content"></div>
        <div :class="[layerVisible ? 'layer-content-active' : '', 'layer-content']" slot="reference">
          <i class="icon-font icon-tucengguanli"></i>
        </div>
      </el-popover>
    </div>
    <div class="layer-description">
      <div>
        <img src="@/assets/img/device-map/layer-description-online.png" />
        在线
      </div>
      <div>
        <img src="@/assets/img/device-map/layer-description-not-online.png" />
        离线
      </div>
      <div>
        <span></span>
        异常
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import common from '@/config/api/common';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'deviceMap',
  props: {},
  data() {
    return {
      searchData: {
        keyWord: '',
        regionCode: '',
      },
      defaultTree: {
        regionCode: '',
      },
      saveLoading: false,
      mapPosition: {
        center: {},
      },
      nodeKey: 'id',
      defaultProps: {
        label: 'orgName',
        children: 'children',
        isLeaf: 'leaf',
      },
      layerVisible: false,
      list: [],
      layerParams: {
        checkStatus: ['0000', '1000', '0100', '0001', '0010', '0011', '0101', '0110', '0111'],
        sbdwlx: ['1', '2', '3', '4', ''],
        sbgnlx: ['1', '2', '3', ''],
      },
      mapOrgTreeVisible: false,
      loading: true,
      chooseMapItem: {},
      cameraList: [],
      geoRegions: [],
      extent: {},
      sbgnlxStatistic: [],
      center: {},
      org: {},
    };
  },
  created() {},
  methods: {
    async sbgnlxGroupStatByOrgCode(orgCode) {
      try {
        const res = await this.$http.get(equipmentassets.sbgnlxGroupStatByOrgCode, {
          params: { orgCode: orgCode },
        });
        this.sbgnlxStatistic = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async selectOrg(org) {
      const areaCode = await this.getArea(org.id);
      await this.getGeoRegions(areaCode);
      this.getOrgDeviceStatistic(org.orgCode);
      this.sbgnlxGroupStatByOrgCode(org.orgCode);
      this.org = org;
    },
    async getArea(orgId) {
      try {
        const res = await this.$http.get(common.getOrgView + `/${orgId}`);
        return res.data.data.regionCode || res.data.data.orgCode;
      } catch (err) {
        console.log(err);
      }
    },
    async getGeoRegions(code) {
      // 地图json文件名字一定是6位的，后端可能返回6位 or 8位，截取
      let reallyAreaCode = code;
      if (!!code && code?.length > 6) {
        reallyAreaCode = code.slice(0, 6);
      }
      try {
        let { data } = await this.$http.get(`/json/map-china-config/${reallyAreaCode}.json`);
        this.geoRegions = data.features.map((row, index) => {
          // fixed:这里来处理geoJSON中的properties存在center会导致地图sdk中报错
          if (row.properties.center) {
            this.$set(row.properties, 'cp', row.properties.center);
            delete row.properties.center;
          }
          index === 0 &&
            (this.center = {
              lon: row.properties.cp[0],
              lat: row.properties.cp[1],
            });
          return row;
        });
      } catch (err) {
        console.log(err, 'err');
      }
    },
    zoomChange(zoom) {
      /**
       * 当地图层级大于11时加载摄像头
       */
      // this.zoom = zoom;
      // if (this.zoom > 11) {
      //   this.getDevicListInPolygon();
      // } else {
      //   this.cameraList = [];
      // }
    },
    dragEnd() {
      // if (this.zoom > 11) {
      //   this.getDevicListInPolygon();
      // } else {
      //   this.cameraList = [];
      // }
    },
    getExtent(extent) {
      this.extent = extent;
    },
    async getOrgDeviceStatistic(orgCode) {
      try {
        this.loading = true;
        const res = await this.$http.get(equipmentassets.getNextLevelPerOrgCodeDeviceCount, {
          params: {
            orgCode,
          },
        });
        const orgDeviceStatistic = res.data.data || [];
        this.geoRegions.forEach((row) => {
          const statistic = orgDeviceStatistic.find((rw) => {
            return rw.regionCode == row.properties.adcode || rw.regionCode == row.properties.id;
          });
          statistic && (row.properties.name = `${row.properties.name}(${statistic.count})`);
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    onDeviceListChange(deviceList){
      const cameraList = [];
      deviceList.forEach((item) => {
        if(item.longitude && item.latitude){
          cameraList.push({...item});
        }
      });
      this.cameraList = cameraList;
      this.getDevicListInPolygon();
    },
    async getDevicListInPolygon() {
      try {
        this.loading = true;
        // const coordinate = [
        //   {
        //     longitude: this.extent.left,
        //     latitude: this.extent.top,
        //   },
        //   {
        //     longitude: this.extent.left,
        //     latitude: this.extent.bottom,
        //   },
        //   {
        //     longitude: this.extent.right,
        //     latitude: this.extent.top,
        //   },
        //   {
        //     longitude: this.extent.right,
        //     latitude: this.extent.bottom,
        //   },
        // ];
        // const res = await this.$http.post(equipmentassets.getDevicListInPolygon, {
        //   points: coordinate,
        //   orgCode: this.org.orgCode,
        // });
        // this.cameraList = res.data?.data || [];
        this.$nextTick(() => {
          const fillerList = this.multiFilter(this.cameraList, this.layerParams);
          this.layersFilter(fillerList);
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    selectDevice({
      latitude,
      longitude,
      isOnline: checkStatus,
      orgCodeName: orgName,
      tagList = [],
      errorMessage = [],
      ...rest
    }) {
      if (!latitude || !longitude) {
        return this.$Message.error('经纬度信息不全！');
      }
      this.chooseMapItem = {
        ...rest,
        orgName,
        longitude,
        latitude,
        checkStatus,
        tagList: tagList.filter((e) => e.tagName),
        errorMessage: errorMessage.filter((e) => e),
      };
    },
    // 图层过滤
    layersFilter(filterCameraList) {
      this.$refs.deviceMap._initSystemPoints2Map([...filterCameraList]);
    },
    multiFilter(array, filters) {
      const filterKeys = Object.keys(filters);
      return array.filter((item) => {
        return filterKeys.every((key) => {
          if (!filters[key].length) {
            return false;
          } else if (item[key] && item[key].includes('/')) {
            return filters[key].find((fil) => item[key].indexOf(fil) !== -1);
          } else {
            return filters[key].indexOf(item[key]) !== -1;
          }
        });
      });
    },
    // 图层筛选条件改变
    layerCondition(e, type, typeVal) {
      // 因为有空类型的存在，所以这里要判断false
      if (e !== false) {
        const list = e.split(',');
        if (list.length > 0) {
          list.forEach((item) => {
            this.layerParams[type].push(item);
          });
        }
      } else {
        let index = this.layerParams[type].indexOf(typeVal);
        if (index > -1) {
          this.layerParams[type].splice(index, 1);
        }
      }
      const fillerList = this.multiFilter(this.cameraList, this.layerParams);
      this.layersFilter(fillerList);
    },
    openMapOrgTree() {
      this.mapOrgTreeVisible = true;
    },
    async queryDeviceInfo(deviceId) {
      let res = await this.$http.post(common.getDeviceInfo, { deviceId });
      this.selectDevice(res.data.data);
    },
  },
  activated() {
    let { deviceId } = this.$route.query;
    if (deviceId) {
      this.queryDeviceInfo(deviceId);
    }
  },
  watch: {
    defaultSelectedOrg: {
      handler(val) {
        val && this.selectOrg(val);
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      mapSelectTreeList: 'common/getMapSelectTreeList',
      checkStatus: 'algorithm/check_status', // 检测状态
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  components: {
    MapBase: require('@/components/map-base/index.vue').default,
    MapSearchTree: require('./map-search-tree.vue').default,
  },
};
</script>
<style lang="less">
.layer-popover {
  margin-top: 0 !important;
  position: absolute !important;
  top: 220px !important;
  z-index: 99999 !important;
  width: 282px !important;
  padding: 0 !important;
  padding-bottom: 10px !important;
  color: var(--color-input);
  .popper__arrow {
    display: none !important;
  }
  .layer-title {
    height: 44px;
    line-height: 44px;
    font-weight: bold;
    border-bottom: 1px solid var(--border-color);
    padding-left: 23px;
  }
  .device-type {
    padding: 11px 13px 4px 23px;
    .device-type-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        display: flex;
        align-items: center;
        .type-bg {
          display: inline-block;
          width: 34px;
          height: 34px;
          margin-right: 10px;
          background-image: url('~@/assets/img/device-map/layer-icon.png');
          background-size: 100% 100%;
          line-height: 34px;
          text-align: center;
          .icon-font {
            background-image: -webkit-linear-gradient(180deg, #5d9be2 0%, #1575f3 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 14px !important;
          }
        }
        .count {
          font-weight: bold;
          color: rgb(187, 112, 57);
          margin-left: 10px;
        }
      }
    }
  }
  .ivu-divider {
    background: var(--border-color);
    margin: 8px 0;
  }
}
</style>
<style lang="less" scoped>
[data-theme='dark'] {
  .layer-popover {
    background: #041d42;
    border: 1px solid #2967c8;
    .layer-title {
      border-bottom: 1px solid #1d4582;
    }
    .ivu-divider {
      background: #1d4582;
    }
    .device-type {
      .type-bg {
        background-image: url('~@/assets/img/device-map/layer-icon.png');
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .layer-popover {
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(0, 21, 41, 0.15);
    .layer-title {
      border-bottom: 1px solid #d8d8d8;
    }
    .ivu-divider {
      background: #d8d8d8;
    }
    .device-type {
      .type-bg {
        width: 32px !important;
        height: 32px !important;
        background-image: url('~@/assets/img/device-map/layer-icon-light.png');
      }
    }
  }
  .layer-description {
    background: #ffffff !important;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016) !important;
    color: rgba(0, 0, 0, 0.8) !important;
  }
  .place-management .layer-management {
    .layer-content {
      width: 46px;
      height: 52px;
      background-image: url('~@/assets/img/device-map/layer-light.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .icon-font {
        color: #888888;
      }
    }
    .layer-content:hover,
    .layer-content-active {
      background-image: url('~@/assets/img/device-map/layer-active-light.png') !important;
      .icon-font {
        color: #f5f5f5;
      }
    }
  }
}
.place-management {
  background-color: var(--bg-content);
  .add-place {
    padding: 0 15px;
  }
  .map-box {
    position: relative;
    width: 100%;
    height: 100%;
    .map-base {
      // margin-left: 300px;
    }
  }
  .mask {
    position: relative;
  }
  .mask:after {
    content: '设备数据加载中...';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    color: var(--color-content);
    text-align: center;
    line-height: 25;
    vertical-align: sub;
    font-size: 26px;
    display: inline-block;
  }
  .map-search {
    position: absolute;
    left: 12px;
    top: 20px;
    width: 31px;
    height: 31px;
    background: var(--bg-btn-primary);//var(--color-el-tree-node__expand-icon)
    border-radius: 4px;
    color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .draw-enter-active,
  .draw-leave-active {
    transition: all 1s ease;
  }
  .draw-enter, .draw-leave-to /* .fade-leave-active below version 2.1.8 */ {
    height: 0;
  }
  .place-box {
    position: absolute;
    left: 11px;
    top: 0px;
    bottom: 8px;
    .area-tree {
      @{_deep}.ivu-select {
        width: 259px;
      }
      @{_deep}.ivu-select-dropdown {
        width: 259px;
      }
    }
    .btn-div {
      display: flex;
      justify-content: space-between;
    }
    .content-tip {
      color: #c76d28;
    }
    .place-list {
      width: 100%;
      @{_deep} .ivu-scroll-container {
        height: 100% !important;
        overflow-y: auto;
      }
      .place-item {
        color: #fff;
        padding: 5px 10px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &:hover {
          background-color: #041129;
        }
        &.active {
          background-color: #184f8d;
        }
      }
    }
  }
  .layer-management {
    position: absolute;
    top: 74px;
    right: 30px !important;
    .layer-content {
      position: relative;
      width: 38px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: url('~@/assets/img/device-map/layer.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .icon-font {
        color: #f5f5f5;
        font-size: 16px;
      }
    }
    .layer-content:hover,
    .layer-content-active {
      background-image: url('~@/assets/img/device-map/layer-active.png') !important;
    }
  }
  .layer-description {
    position: absolute;
    right: 20px;
    bottom: 10px;
    height: 98px;
    width: 72px;
    background: #07295d;
    box-shadow: 0px 3px 10px #000000;
    font-size: 12px;
    color: #f5f5f5;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    > div {
      display: flex;
      align-items: center;
      padding-left: 9px;
      & > span {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: #f15b2d;
        margin-right: 8px;
        margin-left: 4px;
      }
      & > img {
        margin-right: 6px;
      }
    }
  }
}
</style>
