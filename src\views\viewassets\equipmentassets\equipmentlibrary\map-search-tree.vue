<template>
  <div class="search-tree">
    <div class="org-search-tree">
      <div class="input-div">
        <Input :placeholder="placeholder" type="text" v-model="orgName" @keyup.enter.native="enter">
          <template slot="append">
            <i class="icon-font icon-sousuo" @click="enter"></i>
          </template>
        </Input>
      </div>
      <div class="orgtree">
        <div class="title">
          <span>组织机构</span>
          <span @click="$emit('putaway')" class="putaway">收起</span>
        </div>
        <el-tree
          :data="treeData"
          :props="defaultProps"
          :show-checkbox="showCheckbox"
          :node-key="nodeKey"
          :default-expanded-keys="defaultKeys"
          :default-checked-keys="defaultCheckedKeys"
          :default-expand-all="false"
          :expand-on-click-node="false"
          :current-node-key="currentNodeKey"
          :filter-node-method="filterNode"
          :check-strictly="checkStrictly"
          :style="{ maxHeight: maxHeight / 192 + 'rem' }"
          @check="check"
          @check-change="checkChange"
          @node-click="handleNodeClick"
          ref="uiTree"
          class="el-tree"
          v-scroll="scroll"
          :load="loadTreeChildrenNode"
          lazy
        >
          <div class="custom-tree-node" slot-scope="{ node, data }">
            <slot name="label" :node="node" :data="data">
              <div :class="[data.isOnline ? 'custom-tree-node-label' : '']">
                <span
                  :class="{
                    'nav-not-selected': node.disabled,
                    allowed: node.disabled,
                  }"
                >
                  {{ node.label }}
                </span>
                <span v-if="!data.deviceId">
                  （
                  <span class="device-count">
                    <span class="total-count"> {{ data.onlineNum ? data.onlineNum : 0 }} </span>/
                    <span class="online-equipment">
                      {{ data.total ? data.total : 0 }}
                    </span>
                  </span>
                  ）
                </span>
              </div>
            </slot>
          </div>
        </el-tree>
      </div>
    </div>
    <transition-group name="draw">
      <div class="device-list-wrap" v-show="showDeviceList" :key="1">
        <div class="device-search">
          <span>设备列表</span>
          <el-popover
            popper-class="map-popover"
            visible-arrow="false"
            placement="bottom"
            v-model="visible"
            trigger="click"
          >
            <div class="form-content">
              <ui-label class="mb-lg" :label="`${global.filedEnum.deviceId}`" :width="100" align="right">
                <Input
                  class="ml-sm"
                  :placeholder="`请输入${global.filedEnum.deviceId}`"
                  v-model.trim="searchData.deviceId"
                  size="small"
                >
                </Input>
              </ui-label>
              <ui-label class="mb-lg" :label="`${global.filedEnum.deviceName}`" :width="100" align="right">
                <Input
                  :placeholder="`请输入${global.filedEnum.deviceName}`"
                  v-model="searchData.deviceName"
                  size="small"
                  class="ml-sm"
                >
                </Input>
              </ui-label>
              <ui-label class="mb-lg" label="行政区划" :width="100" align="right">
                <api-area-tree
                  class="orgTree ml-sm"
                  :select-tree="selectTree"
                  @selectedTree="selectedArea"
                  placeholder="请选择行政区划"
                ></api-area-tree>
              </ui-label>
              <ui-label class="mb-lg" :label="global.filedEnum.sbdwlx" :width="100" align="right">
                <Select
                  class="width-md ml-sm"
                  v-model="searchData.sbdwlx"
                  :placeholder="`请选择${global.filedEnum.sbdwlx}`"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label class="mb-lg" :label="global.filedEnum.sbgnlx" :width="100" align="right">
                <Select
                  class="width-sm ml-sm"
                  v-model="searchData.sbgnlx"
                  :placeholder="`请选择${global.filedEnum.sbgnlx}`"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in propertySearchLbgnlx" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label class="mb-lg" label="检测状态" :width="100" align="right">
                <Select
                  class="width-sm ml-sm"
                  v-model="searchData.checkStatus"
                  placeholder="请选择检测状态"
                  clearable
                  multiple
                  :max-tag-count="2"
                >
                  <template v-for="(item, index) in checkStatus">
                    <Option :key="index" :value="item.dataKey" v-if="item.dataKey !== '0'"
                      >{{ item.dataValue }}
                    </Option>
                  </template>
                </Select>
              </ui-label>
              <div class="mb-md t-center">
                <Button class="ml-sm" @click="reset">重置</Button>
                <Button class="ml-sm" type="primary" @click="submit">确 定</Button>
              </div>
            </div>
              <i class="icon-font icon-shaixuan f-14" slot="reference"></i>
          </el-popover>
        </div>

        <div @click="showDeviceList = false" class="hide-btn">
          <Icon type="ios-arrow-back" size="16" />
        </div>

        <div
          v-infinite-scroll="handleReachBottom"
          :infinite-scroll-immediate="true"
          infinite-scroll-distance="20"
          class="device-list"
        >
          <div
            class="device-name"
            v-for="(item, index) in deviceList"
            :key="item.id"
            @click="choiceDevice(item, index)"
            :class="[currentIndex === index ? 'active' : '']"
          >
            <span class="icon">
              <i class="icon-font icon-equipmentlibrary online" v-show="item.isOnline === '1'"></i>
              <i class="icon-font icon-equipmentlibrary not-online" v-show="item.isOnline !== '1'"></i>
            </span>
            <span class="name">{{ item.deviceName }}</span>
          </div>
          <!-- <div class="device-loading" v-show="loading">
            <img src="@/assets/img/common/loading.gif" alt="" />
          </div> -->
        </div>

        <div class="no-data" v-show="!deviceList.length">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import common from '@/config/api/common';
export default {
  data() {
    return {
      searchText: '',
      visible: false,
      selectTree: {
        regionCode: '',
      },
      searchData: {
        orgCode: '',
        deviceId: '',
        sbdwlx: '',
        sbgnlx: '',
        checkStatus: [],
        deviceName: '',
        params: {
          pageNumber: 1,
          pageSize: 50,
        },
      },
      originSearchData: {},
      showDeviceList: false,
      deviceList: [],
      treeData: [],
      currentIndex: -1, // 当前选中device,
      orgName: '',
      parentId: '',
      loading: false,
    };
  },
  watch: {
    currentNodeKey(val) {
      this.$refs.uiTree.setCurrentKey(val);
    },
    deviceList(val) {
       this.$emit('onDeviceListChange', val);
    }
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
    }),
  },
  props: {
    placeholder: {
      default: '请输入区域名称或区域编码',
    },
    // 传入的属性对应的值
    defaultProps: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          children: 'children',
          isLeaf: 'leaf',
        };
      },
    },
    // 默认展开的数组
    defaultKeys: {},
    // 默认选中的keys
    defaultCheckedKeys: {},
    // 当前选中的节点
    currentNodeKey: [String, Number],
    // 是否默认全部展开
    expandAll: {},
    // 是否显示checkbox
    showCheckbox: {},

    // 滑动高度
    scroll: {
      type: Number,
    },
    // 最大高度
    maxHeight: {
      type: Number,
    },
    // 关联字段
    nodeKey: {
      required: true,
    },
    // 是否子级父级不关联选择
    checkStrictly: {
      default: false,
      type: Boolean,
    },
  },
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
  },
  async created() {
    await this.getOrgsTreeNew();
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    this.originSearchData = JSON.parse(JSON.stringify(this.searchData));
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    filterNode(value, data) {
      if (!value) return true;
      return data[this.defaultProps.label].indexOf(value) !== -1 || data[this.nodeKey] === value;
    },
    enter() {
      this.getOrgsTreeNew();
      this.deviceList = [];
      this.showDeviceList = false;
    },
    // 节点选中触发
    handleNodeClick(data) {
      if (data.disabled) {
        return false;
      }
      this.$emit('selectOrg', data);
      const { orgCode } = data;
      this.showDeviceList = true;

      this.searchData = {
        orgCode,
        params: {
          pageNumber: 1,
          pageSize: 50,
        },
      };
      this.deviceList = [];
      this.currentIndex = -1;
      this.getOrgDeviceMapList();
    },
    // 选择checkbox触发
    check(data, checkData) {
      this.$emit('check', checkData.checkedKeys, data, checkData);
    },
    checkChange(checkedObj, status) {
      this.$emit('check-change', checkedObj, status);
    },
    setCheckedKeys(data) {
      this.$refs.uiTree.setCheckedKeys(data);
    },
    setChecked(key, bool, includeChild = false) {
      this.$refs.uiTree.setChecked(key, bool, includeChild);
    },
    getCheckedKeys() {
      return this.$refs.uiTree.getCheckedKeys();
    },
    getNode(key) {
      return this.$refs.uiTree.getNode(key);
    },
    getCheckedNodes(isChild = false, hasHalf = false) {
      return this.$refs.uiTree.getCheckedNodes(isChild, hasHalf);
    },
    selectedArea(area) {
      this.searchData.orgCode = area.regionCode;
    },
    submit() {
      this.visible = false;
      this.deviceList = [];
      this.searchData.params.pageNumber = 1;
      this.getOrgDeviceMapList();
    },
    reset() {
      this.searchData = { ...this.originSearchData };
      this.deviceList = [];
      this.getOrgDeviceMapList();
    },
    handleReachBottom() {
      this.searchData.params.pageNumber += 1;
      this.getOrgDeviceMapList();
    },
    async getOrgDeviceMapList() {
      this.loading = true;
      const res = await this.$http.post(common.getConditionQueryPageList, {
        ...this.searchData,
      });
      this.loading = false;
      this.deviceList = this.deviceList.concat(res.data.data.entities);
    },
    async getOrgsTreeNew() {
      const { orgName } = this;
      const res = await this.$http.post(common.getOrgsTreeNew, { orgName });

      this.treeData = res.data.map((e) => {
        return {
          ...e,
          disabled: e.selectedStatus !== 1,
          children:
            e.childCount > 0 && e.children.length > 0
              ? e.children.map((item) => {
                  return {
                    ...item,
                    leaf: item.childCount > 0 ? false : true,
                    disabled: item.selectedStatus !== 1,
                  };
                })
              : e.children,
        };
      });
    },
    loadTreeChildrenNode(node, resolve) {
      if (node.level === 1) {
        resolve(
          node.data.children.length > 1
            ? node.data.children
            : node.data.children.map((e) => {
                return {
                  ...e,
                  leaf: e.childCount > 0 ? false : true,
                };
              }),
        );
      } else {
        const { orgCode, id } = node.data;
        const { orgName } = this;
        this.getChildByOrgCode({ orgCode, orgName, parentId: id }).then(() => {
          resolve(this.orgCodeChildren);
        });
      }
    },
    // 查询org树子集
    async getChildByOrgCode({ orgCode, orgName, parentId }) {
      const res = await this.$http.post(common.getChildByOrgCode, {
        orgCode,
        orgName,
        parentId,
      });
      this.orgCodeChildren = res.data.map((e) => {
        return {
          ...e,
          leaf: e.childCount > 0 ? false : true,
          disabled: e.selectedStatus !== 1,
        };
      });
    },
    // 选中设备
    choiceDevice(item, index) {
      this.currentIndex = index;
      this.$emit('selectDevice', item);
    },
  },
};
</script>
<style lang="less">
.map-popover {
  margin-top: 0 !important;
  position: absolute !important;
  top: 236px !important;
  left: 562px !important;
  z-index: 99999 !important;
  width: 370px !important;
  height: 395px !important;
  padding: 28px 13px 20px 16px !important;
  .popper__arrow {
    display: none !important;
  }
  .form-content {
    .mb-lg {
      display: flex;
      .orgTree {
        .ivu-dropdown {
          .ivu-select-dropdown {
            left: 80px !important;
          }
        }
      }
    }
  }
  .ivu-select-selection,
  .ivu-input-wrapper {
    width: 230px;
  }
  .ivu-select-dropdown {
    width: 230px !important;
  }
}
</style>
<style lang="less" scoped>
[data-theme='dark'] {
  .orgtree {
    background: rgba(7, 41, 93, 0.9);
  }
  .device-list-wrap {
    background: #07295d;
  }
  .map-popover {
    background: #041d42 !important;
    border: 1px solid #2967c8 !important;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .orgtree,
  .device-list-wrap,
  .map-popover {
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(0, 21, 41, 0.15);
    opacity: 1 !important;
  }
  .device-list-wrap .hide-btn {
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(0, 21, 41, 0.15);
    .icon-font {
      color: rgba(0, 0, 0, 0.8) !important;
    }
  }
}
@{_deep}.el-tree-node__expand-icon {
  color: var(--color-primary);
}
@{_deep}.el-tree-node > .el-tree-node__children {
  overflow: initial;
}
@{_deep}.el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
}

.search-tree {
  height: 100%;
  display: flex;
  width: 630px;
  overflow: hidden;
}
.org-search-tree {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 312px;
  padding-right: 2px;
  padding-top: 20px;
  position: relative;
  z-index: 99;
  .input-div {
    margin-bottom: 10px;
  }
  .orgtree {
    border-radius: 4px;
    height: 93.5%;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 39px;
      color: var(--color-content);
      padding: 0 11px 0 19px;
      font-size: 14px;
      border-bottom: 1px solid var(--border-color);
      > span {
        font-weight: bold;
        color: var(--color-content);
      }
      .putaway {
        font-weight: normal;
        color: #2068b8;
        cursor: pointer;
      }
    }
  }

  .el-tree {
    flex: 1;
    height: 93% !important;
    font-size: 14px;
    padding: 8px 0 8px 10px;
    .custom-tree-node {
      width: 100%;
      padding-right: 10px;
      .custom-tree-node-label {
        margin-left: -26px;
      }
      .device-count {
        font-weight: bold;
        .online-equipment {
          color: var(--color-content);
        }
        .total-count {
          color: var(--color-success);
        }
      }

      .icon-font {
        color: #cc3e3a;
        font-size: 14px !important;
        margin-right: 6px;
      }
    }
  }
}
.device-list-wrap {
  margin-top: 100px;
  margin-bottom: 10px;
  width: 300px;
  height: 750px;
  opacity: 0.9;
  transition: width 1s;
  border-radius: 4px;
  position: relative;
  .device-search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 37px;
    border-bottom: 1px solid var(--border-color);
    padding: 0 10px 0 21px;
    > span {
      font-weight: bold;
      color: var(--color-content);
    }
    .icon-font {
      color: var(--color-el-tree-node__expand-icon);
      font-weight: normal;
    }
  }
  .hide-btn {
    position: absolute;
    right: -16px;
    top: 50%;
    transform: translateY(-50%);
    height: 57px;
    width: 16px;
    line-height: 57px;
    background: #0e3165;
    opacity: 1;
    border-radius: 0px 4px 4px 0px;
    .icon-font {
      font-size: 14px;
      color: #f5f5f5;
      font-weight: bold;
    }
  }
  .device-list {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 8px 0;
    height: 700px;
    .device-name {
      padding-left: 35px;
      height: 32px;
      width: 100%;
      line-height: 32px;
      color: var(--color-content);
      font-size: 14px;
      white-space: nowrap;
      cursor: pointer;
      .icon {
        display: inline-block;
      }
      .online {
        font-size: 14px;
        color: #23a179;
      }
      .not-online {
        color: #959a99;
      }
      .name {
        margin-left: 8px;
      }
    }
    .active {
      background-color: var(--bg-vertical-tab-active);
      color: var(--color-vertical-tab-active);
    }
    .device-loading {
      img {
        width: 100%;
      }
    }
  }
}
.draw-enter,
.draw-leave-to {
  width: 0;
  opacity: 0;
  transform: translateX(-300px);
}
.draw-enter-to,
.draw-leave {
  width: 300px;
  opacity: 1;
}
.draw-enter-active,
.draw-leave-active {
  transition: all 2s;
}
@{_deep}.ivu-input-group-append {
  background: var(--bg-btn-primary);
  border: none;
  color: #fff;
  &:hover {
    background: var(--bg-btn-primary);
    border: none;
  }
}
</style>
