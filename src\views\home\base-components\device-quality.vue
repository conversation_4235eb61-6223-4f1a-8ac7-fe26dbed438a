<template>
  <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
    <draw-echarts
      class="charts"
      :echart-option="propertyEchart"
      :echart-style="ringStyle"
      ref="qualityChartRef"
      :echarts-loading="echartsLoading"
      @echartClick="echartClickJump"
    ></draw-echarts>
    <span class="next-echart">
      <i
        class="icon-font icon-zuojiantou1 f-12"
        @click="scrollRight('qualityChartRef', echartList, [], comprehensiveConfig.homeNum)"
      ></i>
    </span>
  </div>
</template>

<script>
import TooltipDom from '@/views/home/<USER>/echarts-dom/tooltip-dom.vue';
import dataZoom from '@/mixins/data-zoom';
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';
import deviceQualityStyle from '@/views/home/<USER>/module/device-quality';

export default {
  name: 'device-quality',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    evaluationIndexResult: {},
    loading: {},
    styleType: {},
  },
  data() {
    return {
      tabData: [{ label: '设备质量', id: 'quality' }],
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      propertyEchart: {},
      activeValue: 'quality',
      echartsLoading: false,
      echartList: [],
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
    homeStyle() {
      return deviceQualityStyle[`style${this.styleType}`] || deviceQualityStyle.style1;
    },
  },
  watch: {
    loading: {
      handler() {
        this.echartsLoading = this.loading;
      },
      immediate: true,
    },
    evaluationIndexResult: {
      async handler(val) {
        if (!val?.length) return;
        await this.getNumRankInfo();
        this.handleQualityCharts();
      },
      immediate: true,
    },
  },
  methods: {
    getIndexItem(indexId) {
      return this.evaluationIndexResult.find((item) => item.indexId === indexId);
    },
    echartClickJump(item) {
      this.$emit('on-jump', item.data.data.originData);
    },
    initCharts() {
      setTimeout(() => {
        this.setDataZoom('qualityChartRef', [], this.comprehensiveConfig.homeNum);
      });
    },
    // 设备质量
    async getNumRankInfo() {
      if (!this.getIndexItem(1001)) return;
      // 找到 填报准确率 1001的数据，然后再请求接口
      let isIndexIdItem = this.getIndexItem(1001);
      this.echartsLoading = true;
      let data = {
        indexId: 1001,
        batchId: isIndexIdItem?.batchId,
        access: 'REPORT_MODE',
        displayType: 'REGION',
        orgRegionCode: isIndexIdItem?.civilCode,
        sortField: 'REGION_CODE',
      };
      try {
        let res = await this.$http.post(governanceevaluation.getNumRankInfo, data);
        this.echartList = res.data.data || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    handleQualityCharts() {
      if (!this.getIndexItem(1001)) return;
      let { indexId, batchId, indexType } = this.getIndexItem(1001);
      let qualityData = [];
      let disQualityData = [];
      let xAxisData = [];
      this.echartList.forEach((item) => {
        let qualityRate = item?.resultValue ?? 0;
        // let titleNum = (item?.qualifiedNum * 100 ?? 0) / ((item?.qualifiedNum + item?.unqualifiedNum) ?? 1)
        let detailData = {
          name: item.regionName,
          title: '填报准确率',
          titleNum: `${qualityRate.toFixed(2)}%`,
          list: [
            {
              label: '合格数量',
              color: this.homeStyle.qualified,
              num: item?.qualifiedNum ?? 0,
            },
            {
              label: '不合格数量',
              color: this.homeStyle.unqualified,
              num: item?.unqualifiedNum ?? 0,
            },
          ],
          originData: {
            ...item,
            indexId: indexId,
            batchId: batchId,
            indexType: indexType,
          },
        };
        qualityData.push({
          value: item.qualifiedNum,
          data: detailData,
          itemStyle: {
            borderWidth: item.qualifiedNum ? 1 : 0,
            borderColor: (() => {
              return new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: this.homeStyle.qualified,
                  },
                  {
                    offset: 1,
                    color: this.homeStyle.qualified,
                  },
                ],
                false,
              );
            })(),
          },
        });
        disQualityData.push({
          value: item.unqualifiedNum,
          data: detailData,
          itemStyle: {
            borderWidth: item.unqualifiedNum ? 1 : 0,
            borderColor: (() => {
              return new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: this.homeStyle.unqualified,
                  },
                  {
                    offset: 1,
                    color: this.homeStyle.unqualified,
                  },
                ],
                false,
              );
            })(),
          },
        });
        xAxisData.push(item.regionName);
      });
      this.propertyEchart = this.$util.doEcharts.getHomeAreaBar({
        toolTipDom: TooltipDom,
        title: '设备数量',
        axisLabelShowSize:6, 
        series: [
          {
            name: '合格',
            color: [this.homeStyle.qualified, this.homeStyle.qualified],
            borderColor: ['rgba(157, 242, 255, 1)', 'rgba(0, 131, 253, 1)'],
            data: qualityData,
          },
          {
            name: '不合格',
            color: [this.homeStyle.unqualified, this.homeStyle.unqualified],
            borderColor: ['rgba(255, 146, 176, 1)', 'rgba(251, 20, 79, 1)'],
            data: disQualityData,
          },
        ],
        xAxisData: xAxisData,
      });
      this.initCharts();
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  width: 100%;
  position: relative;
  height: calc(100% - 30px) !important;
}
.charts {
  width: 100%;
  height: 100% !important;
}
</style>
