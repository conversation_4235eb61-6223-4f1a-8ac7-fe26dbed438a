<template>
  <ui-modal v-model="visible" title="采集场所范围" footer-hide :styles="styles">
    <div :class="[loading ? 'mask' : '', 'map-box']">
      <map-base
        v-if="visible"
        class="map-base"
        ref="mapRef"
        :is-edit="isEdit"
        :bottom-tool="isEdit"
        :camera-list="cameraList"
        :map-position="mapPosition"
        :map-tool="['move', 'polygon', 'clear']"
        :geo-regions="regions"
        :zoom-change="zoomChange"
        :drag-end="dragEnd"
        @selectCamera="selectCamera"
        @putPosition="putPosition"
        @getExtent="getExtent"
      >
        <template #addTool>
          <template v-for="item in addTool">
            <li :key="item.title" :title="item.title" @click="item.fun" class="add-tool-list">
              <i class="icon-font f-20" :class="item.icon"></i>
            </li>
          </template>
        </template>
        <template #deviceSlot>
          <device-list
            :all-device-list="cameraList"
            :default-camera-list="defaultCameraList"
            :is-view="!isEdit"
            @putCameraList="putCameraList"
            @selectedOrgTree="selectedOrgTree"
          ></device-list>
        </template>
      </map-base>
    </div>
  </ui-modal>
</template>
<script>
import { mapActions } from 'vuex';
import common from '@/config/api/common';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    value: {
      type: Boolean,
      require: true,
    },
    mapPosition: {
      type: Object,
    },
    isEdit: {
      type: Boolean,
      require: true,
    },
    defaultDeviceList: {
      type: Array,
    },
    geoRegions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      styles: {
        width: '8rem',
      },
      addTool: [
        { title: '确定', icon: 'icon-dagou', fun: this.query },
        { title: '取消', icon: 'icon-dachasvg', fun: this.cancel },
      ],
      defaultCameraList: [],
      selectedCameraList: [],
      position: {},
      selectedOrgCode: null,
      cameraList: [],
      extent: {},
      zoom: 0,
      regions: [],
      org: {},
    };
  },
  created() {},
  methods: {
    ...mapActions({
      setCameraList: 'common/setCameraList',
    }),
    async selectedOrgTree(org) {
      const areaCode = await this.getArea(org.id);
      await this.getGeoRegions(areaCode);
      this.getOrgDeviceStatistic(org.orgCode);
    },
    async getArea(orgId) {
      try {
        const res = await this.$http.get(common.getOrgView + `/${orgId}`);
        return res.data.data.regionCode || res.data.data.orgCode;
      } catch (err) {
        console.log(err);
      }
    },
    async getGeoRegions(code) {
      try {
        let { data } = await this.$http.get(`/json/map-china-config/${code}.json`);
        this.regions = data.features.map((row) => {
          // fixed:这里来处理geoJSON中的properties存在center会导致地图sdk中报错
          if (row.properties.center) {
            this.$set(row.properties, 'cp', row.properties.center);
            delete row.properties.center;
          }
          return row;
        });
      } catch (err) {
        console.log(err, 'err');
      }
    },
    zoomChange(zoom) {
      /**
       * 当地图层级大于11时加载摄像头
       */
      this.zoom = zoom;
      if (this.zoom > 11) {
        this.getDevicListInPolygon();
      } else {
        this.cameraList = [];
      }
    },
    dragEnd() {
      if (this.zoom > 11) {
        this.getDevicListInPolygon();
      } else {
        this.cameraList = [];
      }
    },
    getExtent(extent) {
      this.extent = extent;
    },
    async getOrgDeviceStatistic(orgCode) {
      try {
        if(!orgCode) return;
        this.loading = true;
        const res = await this.$http.get(equipmentassets.getNextLevelPerOrgCodeDeviceCount, {
          params: {
            orgCode,
          },
        });
        const orgDeviceStatistic = res.data.data;
        this.regions.forEach((row) => {
          const statistic = orgDeviceStatistic.find((rw) => {
            return rw.regionCode == row.properties.adcode || rw.regionCode == row.properties.id;
          });
          statistic && (row.properties.name = `${row.properties.name}(${statistic.count})`);
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async getDevicListInPolygon() {
      try {
        this.loading = true;
        const coordinate = [
          {
            longitude: this.extent.left,
            latitude: this.extent.top,
          },
          {
            longitude: this.extent.left,
            latitude: this.extent.bottom,
          },
          {
            longitude: this.extent.right,
            latitude: this.extent.top,
          },
          {
            longitude: this.extent.right,
            latitude: this.extent.bottom,
          },
        ];
        const res = await this.$http.post(equipmentassets.getDevicListInPolygon, {
          points: coordinate,
        });
        this.cameraList = res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    query() {
      // 这里深度克隆是因为clearDraw会清空列表影响提交的数据
      // 这里转换为字符串后端来接收字符串
      let mapPosition = this.$util.common.deepCopy(this.position);
      let deviceList = this.$util.common.deepCopy(this.selectedCameraList);
      this.$emit('putPlaceMessage', deviceList, mapPosition);
      this.visible = false;
    },
    cancel() {
      this.visible = false;
      this.$refs.mapRef.clearDraw();
    },
    selectCamera(val) {
      this.defaultCameraList = val;
    },
    putCameraList(val) {
      this.selectedCameraList = val;
    },
    putPosition(val) {
      this.position = val;
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    defaultDeviceList(val) {
      this.defaultCameraList = val;
    },
    geoRegions(val) {
      this.regions = val;
    },
  },
  computed: {},
  components: {
    MapBase: require('@/components/map-base/index.vue').default,
    DeviceList: require('@/components/map-base/device-list.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  height: 800px;
}
.map-box {
  position: relative;
  width: 100%;
  height: 100%;
}
.mask {
  position: relative;
}
.mask:after {
  content: '设备数据加载中...';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  text-align: center;
  line-height: 25;
  vertical-align: sub;
  font-size: 26px;
  display: inline-block;
}
.add-tool-list {
  line-height: 40px;
}
</style>
