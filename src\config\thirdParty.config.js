/*
 * @Author: zheng<PERSON><PERSON> zhengming<PERSON>@qishudi.com
 * @Date: 2025-06-20 14:16:10
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-08-27 17:23:40
 * @FilePath: \icbd-viewc:\Users\<USER>\Desktop\项目\ivdg-view\src\config\thirdParty.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Message } from 'view-design';
import store from '../store';
import user from '@/config/api/user';
import axios from '@/config/http/http';

/**
 * key: 1:默认,2:江苏省厅,3:河北(石家庄),4:四川省厅,5:内江市局,6:四川高新区,7:济南市局,8:甘肃省厅,9:兰州,10:乌市 11:兵团
 * 属性 validateParams: 校验各地对应参数是否存在
 * 属性 loginAsync: 对应的登陆方法
 * 属性(pki) isUsePki: 对应属地是否支持Pki
 * 属性(pki) pkiSupport: pki对接厂商： jit(吉大正元)
 * 方法(pki) validatePlugin: 校验插件是否存在
 */
const thirdPartyConfig = {
  '10': {
    validateParams: (params) => {
      if (Object.keys(params).length && params.token) {
        return true;
      } else {
        return false;
      }
    },
    loginAsync: (params) => {
      return new Promise(async (resolve, reject) => {
        try {
          //通过第三方获取我们系统token等信息的接口URL, url获取系统token等信息的的地址
          await store.dispatch('user/getUserTokenByThirdToken', { ...params, url: user.tokenWsUniportalCheck });
          resolve();
        } catch (err) {
          console.log(err);
          // 统一门户地址由后台返回，跳转地址：err.data.data
          err.data?.data ? errorCallBack(err.data.data) : reject(err);
        }
      });
    },
    getUserInfoUrl: user.userWsInfo, //获取用户权限等相关信息的url
  },
  '11': {
    validateParams: async (params, to) => {
      const hasToken = !!window.sessionStorage.getItem('token');
      if (!Object.keys(params).length && !hasToken && to.name !== 'login') {
        try {
          const data = await store.dispatch('user/getBTConfig');
          console.log('=====getBTConfig====', data);
          if (!data.clientId) return false;
          location.href = `${btSsoUrl}?client_id=${data.clientId}&response_type=authorization_code&redirect_uri=${window.location.href}&scope=all HTTP/1.1`;
        } catch (e) {
          return false;
        }
      }
      if (!hasToken && Object.keys(params).length && params.code) {
        return true;
      } else {
        return false;
      }
    },
    loginAsync: (params) => {
      return new Promise(async (resolve, reject) => {
        try {
          //通过第三方获取我们系统token等信息的接口URL, url获取系统token等信息的的地址
          // localStorage.setItem('thirdPartyToken', params.code);
          await store.dispatch('user/getUserTokenByThirdToken', { token: params.code, url: user.btOauthLogin });
          resolve();
        } catch (err) {
          console.log(err);
          // 统一门户地址由后台返回，跳转地址：err.data.data
          err.data?.data ? errorCallBack(err.data.data) : reject(err);
        }
      });
    },
    logout: async () => {
      const token = sessionStorage.getItem('token');
      const res = await axios.get(user.btLogout, {
        params: {
          token,
        },
      });
      console.log('=====btLogout====', res);
      return;
    },
    getUserInfoUrl: async () => {
      try {
        return await store.dispatch('user/getUserInfo', null, {
          root: true,
        });
      } catch (e) {
        throw e;
      }
    }, //获取用户权限等相关信息的url
  },
  '4': {
    isUsePki: true,
    pkiSupport: 'jit', //吉大正元
    validatePlugin: () => {
      //是否安装插件
      try {
        JIT_GW_ExtInterface.GetVersion();
        return true;
      } catch (err) {
        console.log(err);
        return false;
      }
    },
    loginAsync: async () => {
      return new Promise(async (resolve, reject) => {
        try {
          //1、获取随机数
          const original = await store.dispatch('userpki/getOriginal');
          if (!original) {
            reject('获取随机数失败');
            return;
          }
          //2、根据原文和证书产生认证数据包
          // 调用网关工具脚本中的detachSignStr方法进行签名，返回签名结果（public\js\pki\GWUtil.js）
          const signResult = detachSignStr(INIT_PARAM, original.original, '');
          if (!signResult) {
            reject('获取签名结果失败');
            return;
          }
          //3、获取系统认证证书
          const certAuth = {
            original: original.original,
            originalData: original.original_data,
            signedData: signResult,
            authMode: AUTH_MODE.cert,
            remoteAddr: window.location.hostname,
          };
          const pkiUserInfo = await store.dispatch('userpki/getJitPKIAuth', certAuth);
          if (!pkiUserInfo) {
            reject('获取用户认证信息失败');
            return;
          }
          if (!pkiUserInfo.cn) {
            reject('用户登录信息获取失败');
            return;
          }
          //4、 获取系统的登录相关信息
          let [name, IDNumber] = pkiUserInfo.cn.split(' ');
          console.log(name);
          let pkiLoginParam = {
            yhId: IDNumber,
          };
          await store.dispatch('userpki/jitPkiLoigin', pkiLoginParam);
          resolve();
        } catch (err) {
          console.log(err);
          reject(err);
        }
      });
    },
    getUserInfoUrl: user.basicGetUserInfo,
  },
  // 指定某个用户登录
  'useDesignatedUser': {
    loginAsync: async () => {
      await store.dispatch('user/loginUserByThird');
    },
  },
};

/**
 * 登陆失败的回调
 * @param {string} redirectUrl 门户网站地址
 */
const errorCallBack = (redirectUrl) => {
  Message.warning('登陆失败，即将跳转到门户登录页');
  const timer = setTimeout(() => {
    console.error('登陆失败，跳转到门户登录页');
    window.location.href = redirectUrl;
    clearTimeout(timer);
  }, 2000);
};

/**
 * jit pki登录
 * INIT_PARAM：vctk控件初始化参数
 * AUTH_MODE：认证方式
 */
//vctk控件初始化参数
const INIT_PARAM =
  '<?xml version="1.0" encoding="utf-8"?><authinfo><liblist><lib type="CSP" version="1.0" dllname="R0FTU19GIENyeXB0b2dyYXBoaWMgU2VydmljZSBQcm92aWRlciB2MS4w"><algid val="SHA1" sm2_hashalg="SM3" /></lib><lib type="CSP" version="1.0" dllname="R0FTUyBDcnlwdG9ncmFwaGljIFNlcnZpY2UgUHJvdmlkZXIgdjEuMA==" ><algid val="SHA1" sm2_hashalg="sm3"/></lib><lib type="CSP" version="1.0" dllname="TWljcm9zb2Z0IEVuaGFuY2VkIENyeXB0b2dyYXBoaWMgUHJvdmlkZXIgdjEuMA=="><algid val="SHA1" sm2_hashalg="SHA1" /></lib><lib type="CSP" version="1.0" dllname="TWljcm9zb2Z0IFN0cm9uZyBDcnlwdG9ncmFwaGljIFByb3ZpZGVy"><algid val="SHA1" sm2_hashalg="SHA1" /></lib><lib type="CSP" version="1.0" dllname="ZVNhZmUgQ3J5cHRvZ3JhcGhpYyBTZXJ2aWNlIFByb3ZpZGVyIHYxLjA="><algid val="SHA1" sm2_hashalg="SHA1" /></lib><lib type="CSP" version="1.0" dllname="TWljcm9zb2Z0IEJhc2UgQ3J5cHRvZ3JhcGhpYyBQcm92aWRlciB2MS4w"><algid val="SHA1" sm2_hashalg="SHA1" /></lib><lib type="SKF" version="1" dllname="R0FLRVlfU0tGLmRsbA=="><algid val="SHA1" sm2_hashalg="SM3" /></lib><lib type="SKF" version="1.0" dllname="bGliU0tGX0dBX2FwaS5zbw=="><algid val="SHA1" sm2_hashalg="SM3" /></lib></liblist></authinfo>';
const AUTH_MODE = {
  cert: 'cert', //证书
  QRCode: 'QRCode', //二维码
};
export default thirdPartyConfig;
