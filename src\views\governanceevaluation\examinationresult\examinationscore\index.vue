<template>
  <div class="page-assessmentresult">
    <div class="right-content" v-if="!componentName">
      <div class="search-wrapper">
        <ui-label class="inline" label="考核任务">
          <Select class="width-md" placeholder="请选择考核任务" v-model="searchData.examTaskId" @on-change="changeTask">
            <Option v-for="item in taskList" :key="item.id" :value="item.id">
              {{ item.taskName }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="ml-lg inline" label="考核时间">
          <template>
            <DatePicker
              type="month"
              format="yyyy-MM"
              placeholder="请选择考核时间"
              :options="examTimeOptions"
              v-model="searchData.time"
              @on-change="
                () => {
                  searchBtn();
                }
              "
            ></DatePicker>
          </template>
        </ui-label>
        <Button type="primary" class="mr-sm ml-lg" @click="searchBtn">查询</Button>
        <Button @click="resetSearch">重置</Button>
        <CheckboxGroup class="inline ml-lg" v-model="overBtn" @on-change="onChangeModel">
          <Checkbox :label="1" :disabled="modelDisable(1)">统计图</Checkbox>
          <Checkbox :label="2" :disabled="modelDisable(2)">报表</Checkbox>
        </CheckboxGroup>
        <ui-label :width="0" label=" " class="inline fr" v-if="overBtn.includes(2)">
          <Button
            class="mr-sm download-scheme"
            type="text"
            @click="handleUploadDetectDetail('upload')"
            :disabled="!searchData.time || examTimeDisabledArray.includes(`${searchData.time}`)"
            v-permission="{ route: $route.name, permission: 'uploadDetectDetail' }"
            >上传检测明细</Button
          >
          <Button
            class="mr-sm download-scheme"
            type="text"
            @click="handleUploadDetectDetail('download')"
            :disabled="!searchData.time || examTimeDisabledArray.includes(`${searchData.time}`)"
            v-permission="{ route: $route.name, permission: 'downloadDetectDetail' }"
            >下载检测明细</Button
          >
          <Button
            class="mr-sm download-scheme"
            type="text"
            @click="editExaminationScore"
            :disabled="!searchData.time || examTimeDisabledArray.includes(`${searchData.time}`)"
            v-permission="{ route: $route.name, permission: 'postStatisticsSave' }"
          >
            编辑考核成绩
          </Button>
          <Button
            class="mr-sm download-scheme"
            type="text"
            @click="downLoadSchemeUrl"
            :disabled="!searchData.time || examTimeDisabledArray.includes(`${searchData.time}`)"
            >查看考核方案</Button
          >
          <Button
            type="primary"
            class="fr"
            @click="exportHandle"
            :loading="btnLoading"
            :disabled="!searchData.time || examTimeDisabledArray.includes(`${searchData.time}`)"
          >
            <i class="icon-font icon-daochu f-12 mr-sm vt-middle"></i>
            <span class="vt-middle">导出</span>
          </Button>
        </ui-label>
      </div>
      <div class="title-center">
        <div class="conter-center">
          <span> {{ tableTitleYear }}-{{ tableTitleTaskName }} </span>
        </div>
      </div>
      <div v-if="overBtn.includes(1)" class="statistica-overview">
        <statisticalGraph
          :search="searchData"
          :data="taskList"
          :echart-list="echartList"
          :echarts-loading="echartsLoading"
          @on-sort="handleSort"
          @echartClick="echartClick"
        />
      </div>
      <div class="jump mt-md" v-if="overBtn.includes(2)">
        <div class="is-show" @click="isOpen">
          <i :class="['icon-font', 'f-12', isShow ? 'icon-zhankai' : 'icon-xingzhuang']"></i>
          <span v-if="!isShow" class="vt-middle">展开考核内容</span>
          <span v-else>收缩考核内容</span>
        </div>
      </div>
      <template v-if="overBtn.includes(2)">
        <div class="table-box">
          <ui-table
            ref="assessmentTable"
            class="ui-table"
            :table-columns="tableColumns"
            :table-data="tableData"
            :loading="loading"
            :row-class-name="rowClassName"
            full-border
          >
            <template #ORG_REGEION_CODE="{ row, column }">
              <span
                :class="row.clickable ? 'span-btn' : 'dis-span-btn'"
                @click="administrativeDivisionBtn(row, column)"
              >
                {{ row.ORG_REGEION_CODE }}
              </span>
            </template>
          </ui-table>
        </div>
      </template>
      <picture-attribute-integrity
        ref="pictureAttributeIntegrity"
        :task-obj="infoObj"
        @jump="handleClickJump"
        @selectModuleClick="selectModuleHandle"
      ></picture-attribute-integrity>
      <index-detail ref="indexDetail" @showHkDetail="showHkDetail" @showYsDetail="showYsDetail"></index-detail>
      <hk-detail v-model="hkShow" :title="hkTitle" :hk-params="hkParams"></hk-detail>
      <ys-detail v-model="ysShow" :title="ysTitle" :ys-params="ysParams"></ys-detail>
      <edit-examination-score @on-search="onSearch" ref="editExaminationScore"> </edit-examination-score>
      <upload-detect-detail
        v-if="uploadDetectDetailVisible"
        v-model="uploadDetectDetailVisible"
        v-bind="uploadDetectDetailProps"
        @input="changeUploadDetectDetailVisible"
      ></upload-detect-detail>
    </div>
    <keep-alive v-else>
      <component :is="componentName" @changeComponent="changeComponentHandle"></component>
    </keep-alive>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
import { mapGetters } from 'vuex';
import MathJax from '@/components/math-jax/index.vue';
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin';
export default {
  mixins: [dealWatch, evaluationoResultMixin],
  inject: {
    examSchemeType: {
      value: 'examSchemeType',
      default: 1,
    },
  },
  name: 'examinationscore',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    pictureAttributeIntegrity: require('@/views/appraisaltask/assessmenTask/components/picture-attribute-integrity.vue')
      .default,
    indexDetail: require('@/views/governanceevaluation/examinationresult/components/index-detail.vue').default,
    statisticalGraph: require('./components/statistical-graph.vue').default,
    HkDetail: require('@/views/governanceevaluation/examinationresult/components/hk-detail.vue').default,
    YsDetail: require('@/views/governanceevaluation/examinationresult/components/ys-detail.vue').default,
    detectionToOverview: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue')
      .default,
    FaceCarOnlineNum: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-online-num.vue')
      .default,
    FaceCarTopOnline: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-top-online.vue')
      .default,
    FaceCarReportRate:
      require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-report-rate.vue').default,
    VideoHistoryOnline:
      require('@/views/governanceevaluation/evaluationoverview/components/video/video-history-online.vue').default,
    OnOffLineDetail: require('@/views/governanceevaluation/evaluationoverview/components/video/on-off-line-detail.vue')
      .default,
    EditExaminationScore: require('./components/edit-examination-score.vue').default,
    UploadDetectDetail: require('./components/upload-detect-detail.vue').default,
  },
  data() {
    return {
      loading: false,
      echartsLoading: false,
      hkShow: false,
      hkTitle: '',
      hkParams: {},
      ysShow: false,
      ysTitle: '',
      ysParams: {},
      overBtn: [1, 2],
      echartList: [],
      tableTitleYear: '',
      infoObj: '',
      taskList: [],
      firstOrgCode: '',
      taskInfo: {},
      tableTitleTaskName: '',
      orgCode: '',
      searchData: {
        examSchemeId: '',
        examTaskId: '',
        tableTitleTaskName: '',
        time: '',
        month: '',
        year: '',
        orgRegeionCode: '',
        evaluationTaskSchemeId: '',
        scoreDesc: false,
      },
      searchDataCopy: {},
      tableColumns: [
        {
          title: '行政区划',
          key: 'ORG_REGEION_CODE',
          slot: 'ORG_REGEION_CODE',
        },
      ],
      tableData: [],
      breadcrumbData: [],
      btnLoading: false,
      statisticsData: {},
      isShow: false, //列表是否展开
      schemeUrl: '',
      componentName: null,
      taskHistoryList: [], //历史任务列表
      examTimeOptions: {
        disabledDate: (date) => {
          return this.examTimeDisabledArray.includes(this.$util.common.formatDate(date, 'yyyy-MM'));
        },
      },
      examTimeDisabledArray: [],
      uploadDetectDetailVisible: false, //上传、下载检测明细
      uploadDetectDetailProps: {
        //传递给上传下载检测明细的props
        modalType: 'upload',
        taskInfo: {},
      },
      jumpIntoParams: {},
    };
  },
  watch: {},
  created() {
    this.getParams();
  },
  async activated() {
    this.startWatch('$route', () => {
      this.getParams();
    });
    //激活时重新获取未发布时间
    await this.activatedResetTime();
    const params = this.$route.query;
    if (Object.keys(params).length && JSON.stringify(this.jumpIntoParams) !== JSON.stringify(params)) {
      this.jumpIntoParams = params;
      if ('examTaskId' in this.jumpIntoParams) {
        this.changeTask(this.jumpIntoParams.examTaskId, true);
      }
    }
  },
  mounted() {
    this.$http
      .post(governanceevaluation.getAlltaskList, {
        schemeType: this.examSchemeType,
      })
      .then((res) => {
        let { data } = res.data;
        this.taskList = data;
        // 首页跳转需带配置任务
        'examTaskId' in this.$route.query
          ? this.changeTask(this.$route.query.examTaskId, true)
          : this.changeTask(this.taskList.length ? this.taskList[0].id : '', true);
        this.searchDataCopy = JSON.parse(JSON.stringify(this.searchData));
        this.getStatisticalModel(this.searchDataCopy);
      })
      .catch((err) => {
        console.log(err);
      });
  },
  methods: {
    handleSort(val) {
      this.getStatisticalModel({
        ...this.searchData,
        scoreDesc: val,
      });
    },
    onSearch() {
      this.breadcrumbData = [];
      this.search(true);
    },
    editExaminationScore() {
      let task = this.taskList.find((item) => {
        return item.id === this.searchData.examTaskId;
      });
      if (!task) return this.$Message.error('请选择考核任务');
      let { month, year } = this.getDate();
      let { id, schemeId, regionCode, taskName } = task;
      let params = {
        examSchemeId: schemeId,
        examTaskId: id,
        orgRegeionCode: regionCode,
        tableTitleTaskName: taskName,
        time: this.searchData.time,
        month,
        year,
      };
      this.$refs.editExaminationScore.init(params);
    },
    getDate() {
      let date = new Date(this.searchData.time);
      let month = date.getMonth() + 1;
      let year = date.getFullYear();
      return {
        month,
        year,
      };
    },
    echartClick() {
      return;
      // 图表优化
      // let lastArr = this.breadcrumbData[this.breadcrumbData.length - 1];
      // let { ORG_REGEION_CODE, orgCode, clickable } = this.tableData.find((value) => value.ORG_REGEION_CODE === name);
      // if (lastArr['id'] === orgCode) {
      //   return;
      // }
      // if (!clickable) {
      //   return this.$Message.error('您没有此权限');
      // }
      // this.breadcrumbData.push({
      //   id: orgCode,
      //   add: ORG_REGEION_CODE,
      // });
      // this.searchData.orgRegeionCode = orgCode;
      // this.search();
    },
    onChangeModel(val) {
      if (val.length === 0) {
        return this.$Message.warning('至少保留一个');
      }
    },
    rowClassName(row) {
      if (!!this.$route.query.orgCode && row.orgCode === this.$route.query.orgCode) {
        return 'ivu-table-row-hover';
      }
    },
    changeComponentHandle() {
      this.componentName = 'detectionToOverview';
    },
    // 统计模式
    async getStatisticalModel(val) {
      this.echartsLoading = true;
      try {
        let res = await this.$http.post(governanceevaluation.statisticalModel, val);
        this.echartList = res.data.data || [];
      } catch (err) {
        this.echartsLoading = false;
      } finally {
        this.echartsLoading = false;
      }
    },
    handleChange(val) {
      this.searchData.orgRegeionCode = val.id;
      this.search();
    },
    administrativeDivisionBtn(row, column) {
      if (!this.searchData.time || this.examTimeDisabledArray.includes(this.searchData.time)) {
        console.log('考核成绩未发布');
        return;
      }
      if (!row.clickable) {
        return this.$Message.error('您没有此权限');
      }
      // 总分取总分一列
      column.code = 'TOTAL_SCORE';
      //四川省省厅直接查看该组织机构详情，不再查看下级
      if (row.TOTAL_SCORE) {
        this.$refs.indexDetail.init(
          {
            row,
            column,
          },
          this.searchData,
        );
      } else {
        this.$Message.warning('此行政区划没有考核成绩！');
      }
    },
    pictureAttributeIntegrityBtn(row) {
      this.infoObj = {
        ORG_REGEION_CODE: row.row.ORG_REGEION_CODE,
        title: row.column.title,
        parentTitle: row.column.parentTitle,
        score: row.column.score,
        examContentItemMonthResultId: row.row[row.column.key].examContentItemMonthResultId,
      };
      this.$refs.pictureAttributeIntegrity.init();
    },
    async changeTask(id, flag) {
      try {
        if (id) {
          var item = this.taskList.find((v) => {
            return v.id == id;
          });
          this.taskInfo = item;
          this.searchData.tableTitleTaskName = item.taskName;
          this.searchData.time = '';
          this.searchData.month = '';
          this.searchData.year = '';
          if (item.taskRunState !== '0') {
            this.searchData.examTaskId = item.id;
            this.searchData.examSchemeId = item.schemeId;
            this.firstOrgCode = item.regionCode;
            this.orgCode = item.regionCode;
            this.searchData.orgRegeionCode = item.regionCode;
            // 如果是跳转进入此页且带入时间则使用带入的时间
            let time = this.jumpIntoParams.time && flag ? this.jumpIntoParams.time : item.taskStartTime;
            //获取任务记录，未发布禁选
            const startYearMonth = this.$util.common.formatDate(time, 'yyyy-MM');
            await this.getTaskHistoryList();
            if (this.examTimeDisabledArray.includes(startYearMonth)) {
              this.echartList = [];
              this.tableData = [];
              return;
            }
            //月考核-显示年月
            this.searchData.time = startYearMonth;
            this.searchData.year = parseInt(this.$util.common.formatDate(item.taskStartTime, 'yyyy'));
            this.searchData.month = parseInt(this.$util.common.formatDate(item.taskStartTime, 'MM'));
            //增加参数
            this.searchData.evaluationTaskSchemeId = item.evaluationTaskSchemeId || '';
            this.getSelectSchemeUrl();
            if (flag) {
              this.search(flag);
            }
          } else {
            this.$Message.error('考核任务还未开始');
            this.tableData = [];
            this.breadcrumbData = [];
          }
        }
      } catch (err) {
        console.log(err);
      }
    },
    searchBtn() {
      this.breadcrumbData = [];
      if (this.taskInfo.taskRunState === '0') {
        this.$Message.error('考核任务还未开始');
        return;
      }
      this.searchData.orgRegeionCode = this.firstOrgCode;
      this.search(true);
    },
    async search(flag) {
      try {
        this.loading = true;
        this.isShow = false;
        this.tableTitleYear = this.$util.common.formatDate(this.searchData.time, 'yyyy年MM月');
        //月考核-显示年月
        this.searchData.time = this.$util.common.formatDate(this.searchData.time, 'yyyy-MM');
        this.tableTitleTaskName = this.searchData.tableTitleTaskName;
        this.searchData.year = parseInt(this.$util.common.formatDate(this.searchData.time, 'yyyy'));
        this.searchData.month = parseInt(this.$util.common.formatDate(this.searchData.time, 'MM'));
        this.getStatisticalModel(this.searchData);
        const res = await this.$http.post(governanceevaluation.getExamStatistics, this.searchData);
        this.statisticsData = this.$util.common.deepCopy(res.data.data);
        let { headers, body } = res.data.data;
        // let { headers, body } = moke
        // 处理表头
        this.handleTableHeaders(headers);
        this.tableColumns = headers;
        for (let i of this.tableColumns) {
          i.show = true;
          if (i.name === '排名' || i.name === '总分') {
            i.sortable = true;
            i.sortMethod = (a, b, type) => {
              if (a && b) {
                if (type === 'asc') {
                  return a.score < b.score ? -1 : 1;
                } else {
                  return a.score > b.score ? -1 : 1;
                }
              }
            };
          }
          if (i.name === '行政区划' || i.name === '排名' || i.name === '总分') {
            i.fixed = 'left';
          }
        }
        // 处理表内容
        this.handleTableBody(body, flag);
        this.changeCol();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    isOpen() {
      this.isShow = !this.isShow;
      this.changeCol();
    },
    changeCol() {
      if (this.isShow) {
        //展开
        this.tableColumns.forEach((item) => {
          item.show = true;
          if (item.children_str) {
            item.children = item.children_str;
            delete item.children_str;
          }
          if (item.score) {
            item.code = item.code.replace('_X', '');
          }
        });
      } else {
        this.tableColumns.forEach((item) => {
          item.show = false;
          if (item.score) {
            item.code = item.code + '_X';
          }
          if (item.children) {
            item.children_str = item.children;
            delete item.children;
          }
        });
      }
      this.handleTableHeaders(this.tableColumns);
    },
    renderTooltip(h, params) {
      return h(
        'Tooltip',
        {
          props: {
            placement: 'right-start',
            // prop: {
            transfer: true,
            // },
          },
        },
        [
          h(
            'div',
            {
              slot: 'content',
            },
            [
              h(
                'div',
                {
                  class: ['tooltip-title'],
                },
                `${params.column.name}：`,
              ),
              h('div', {}, [
                h('span', {}, '分项分值：'),
                h('span', { class: ['active-blue'] }, `${params.column.score}分`),
              ]),
              h('div', {}, [
                h('span', {}, '实际分值：'),
                h(MathJax, {
                  class: ['active-blue'],
                  props: {
                    mathStr: params.column.examFormula || '',
                  },
                }),
              ]),
              h('div', {}, [h('span', {}, `考核方法：${params.column.examWay || ''}`)]),
            ],
          ),
          h('i', {
            class: ['icon-font', 'icon-wenhao', 'ml-xs', 'icon-warning'],
          }),
        ],
      );
    },
    handleTableHeaders(arr, title) {
      arr.forEach((v) => {
        v.title = v.name;
        v.key = v.code;
        v.className = 'header-table';
        v.align = 'center';
        v.parentTitle = title;
        if (v.score || v.score == 0) {
          v.renderHeader = this.renderTableHeader;
        } else {
          delete v.score;
        }
        if (v.code === 'ORG_REGEION_CODE') {
          v.slot = 'ORG_REGEION_CODE';
          v.minWidth = 160;
        } else if (v.code === 'TOTAL_SCORE') {
          v.minWidth = 100;
        } else {
          v.minWidth = v.title.length * 18;
        }
        if (v.children && v.children.length) {
          this.handleTableHeaders(v.children, v.title);
        } else {
          if (v.code !== 'ORG_REGEION_CODE') v.render = this.renderTableTd;
          delete v.children;
        }
      });
    },
    handleTableBody(arr, flag) {
      let { regionCode } = this.taskList.find((value) => value.id === this.searchData.examTaskId);
      let item = arr.length && arr[0].length ? arr[0][0] : '';
      this.orgCode = item.orgRegeionCode;
      //首次加载
      if (flag) {
        this.breadcrumbData.push({
          id: this.orgCode,
          add: item.orgRegeionName,
        });
      }
      let tableData = [];
      arr.some((v) => {
        let obj = {};
        let flag = false;
        v.some((k) => {
          if (k.orgRegeionCode === regionCode) {
            flag = true;
            return true;
          }
          if (k.code === 'ORG_REGEION_CODE') {
            obj.ORG_REGEION_CODE = k.orgRegeionName;
            obj.orgCode = k.orgRegeionCode;
            obj.clickable = k.clickable;
          } else if (k.code === 'RANKING') {
            obj[k.code] = {
              score: k.rank,
            };
          } else {
            obj[k.code] = {
              score: k.score,
              examContentItemMonthResultId: k.examContentItemMonthResultId,
            };
          }
        });
        if (!flag) {
          tableData.push(obj);
        }
      });
      this.tableData = tableData;
    },
    resetSearch() {
      this.breadcrumbData = [];
      this.changeTask(this.searchDataCopy.examTaskId, true);
    },
    // 导出
    async exportHandle() {
      try {
        this.btnLoading = true;
        let params = this.statisticsData;
        if (this.isShow) {
          //展开
          let data = await this.$http.post(governanceevaluation.exportAssessment, params, {
            responseType: 'blob',
          });
          await this.$util.common.exportfile(
            data,
            `${this.tableTitleYear}-${
              this.breadcrumbData.length ? this.breadcrumbData[this.breadcrumbData.length - 1].add : ''
            }${this.tableTitleTaskName} -【IVDG】-【${this.$util.common.formatDate(new Date())}】`,
            'application/vnd.ms-excel;charset=UTF-8',
          );
        } else {
          //收缩
          let data = await this.$http.post(governanceevaluation.exportExcel, params, {
            responseType: 'blob',
          });
          await this.$util.common.exportfile(
            data,
            `${this.tableTitleYear}-${
              this.breadcrumbData.length ? this.breadcrumbData[this.breadcrumbData.length - 1].add : ''
            }${this.tableTitleTaskName} -【IVDG】-【${this.$util.common.formatDate(new Date())}】`,
            'application/vnd.ms-excel;charset=UTF-8',
          );
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.btnLoading = false;
      }
    },
    selectModuleHandle() {
      this.selectModule(this.$route.query.componentName);
    },
    handleClickJump({ evaluationBatchId, indexId, indexType, orgRegeionCode }) {
      // 考核成绩只有 行政区划
      let { evaluationTaskSchemeId } = this.taskList.find((item) => item.id === this.searchData.examTaskId);
      this.jump({
        orgCode: '',
        regionCode: orgRegeionCode,
        statisticType: 'REGION',
        taskSchemeId: evaluationTaskSchemeId,
        indexId: `${indexId}`,
        indexType: indexType,
        batchId: evaluationBatchId,
      });
    },
    selectModule(name) {
      if (name) {
        const nameArr = name.split('-');
        this.componentLevel = nameArr[nameArr.length - 1] === 'detectionToOverview' ? 0 : 1;
      }
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    async getSelectSchemeUrl() {
      try {
        const res = await this.$http.get(governanceevaluation.selectSchemeUrl, {
          params: {
            schemeId: this.searchData.examSchemeId,
          },
        });
        this.schemeUrl = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    //获取任务历史记录，用于限制展示考核成绩
    async getTaskHistoryList() {
      if (!this.searchData.examTaskId) {
        return;
      }
      try {
        const { data } = await this.$http.get(governanceevaluation.queryTaskHistoryList, {
          params: {
            examTaskId: this.searchData.examTaskId,
          },
        });
        this.taskHistoryList = data.data || [];

        if (data.data.length) {
          let unPublishArr = data.data.filter((item) => item.publishStatus === '0');
          if (!unPublishArr.length) {
            this.examTimeDisabledArray = [];
            return;
          }
          this.examTimeDisabledArray = unPublishArr.map((item) => {
            let examTimeDate = this.$util.common.parseDate(item.examTime);
            return this.$util.common.formatDate(examTimeDate, 'yyyy-MM');
          });
        } else {
          this.examTimeDisabledArray = [];
        }
      } catch (err) {
        console.log(err);
      }
    },
    downLoadSchemeUrl() {
      if (this.schemeUrl) {
        this.$util.common.transformBlob(this.schemeUrl);
      } else {
        this.$Message.error('还未上传考核方案文档');
      }
    },
    renderTableHeader(h, params) {
      return h('div', [
        h('div', {}, [
          h('span', {}, `${params.column.title}  ( ${params.column.score}分 )`),
          params.column.examFormula && this.renderTooltip(h, params),
        ]),
        h(
          'i',
          {
            class: ['icon-font ml-xs f-12', params.column.show ? 'icon-xingzhuang' : 'icon-zhankai'],
            style: {
              cursor: 'pointer',
              display: params.column.parentTitle === undefined ? 'inline-block' : 'none',
            },
            on: {
              click: () => {
                this.tableColumns[params.index].show = !this.tableColumns[params.index].show;
                // 收缩
                if (!this.tableColumns[params.index].show) {
                  this.tableColumns[params.index].code = this.tableColumns[params.index].code + '_X';
                  this.tableColumns[params.index].children_str = this.tableColumns[params.index].children; //备份child
                  delete this.tableColumns[params.index].children;
                  this.handleTableHeaders(this.tableColumns);
                } else {
                  this.tableColumns[params.index].code = this.tableColumns[params.index].code.replace('_X', '');
                  this.tableColumns[params.index].children = this.tableColumns[params.index].children_str; //备份child
                  delete this.tableColumns[params.index].children_str;
                  this.handleTableHeaders(this.tableColumns);
                }
              },
            },
          },
          '',
        ),
      ]);
    },
    renderTableTd(h, params) {
      let clickable = params.row.clickable;
      // if (params.column.key === 'TOTAL_SCORE' || params.column.key === 'RANKING') {
      // 朱耀 现场反馈 超额推送数量  得分 不支持点击跳转
      if (
        params.column.key === 'TOTAL_SCORE' ||
        !params.row[params.column.key] ||
        params.column.key === 'RANKING' ||
        params.column.name.endsWith('超额推送数量') ||
        params.column.name === '得分'
      ) {
        return h(
          'span',
          {
            attrs: {
              class: params.index === 0 ? 'active-blue' : '',
            },
          },
         this.getScore(params.row[params.column.key].score),
        );
        // 判断大类处理点击事件
      } else {
        return h(
          'span',
          {
            style: {
              color: 'var(--color-primary)',
              cursor: clickable ? 'pointer' : 'auto',
              textDecoration: clickable ? 'underline' : 'none',
            },
            attrs: {
              // class: params.index === 0 ? 'active-blue' : '',
            },
            on: {
              click: () => {
                if (!clickable) {
                  return this.$Message.error('您没有此权限');
                }
                if (params.column.children_str !== undefined) {
                  this.$refs.indexDetail.init(params, this.searchData);
                } else {
                  this.pictureAttributeIntegrityBtn(params);
                }
              },
            },
          },
          this.getScore(params.row[params.column.key].score),
        );
      }
    },
    getScore(score) {
      //截取小数点后五位
      const decimalPlaces = 5;
      return score ? score.toString().substring(0, score.toString().indexOf('.') + decimalPlaces + 1) : '--';
    },
    showHkDetail(row) {
      this.hkTitle = row.categoryName;
      this.hkParams = row;
      this.hkShow = true;
    },
    showYsDetail(row) {
      this.ysTitle = row.categoryName;
      this.ysParams = Object.assign(
        {
          schemeId: this.searchData.examSchemeId,
        },
        row,
      );
      this.ysShow = true;
    },
    modelDisable(val) {
      return this.overBtn[0] === val && this.overBtn.length === 1;
    },
    //激活组件时重新判断成绩是否未发布
    async activatedResetTime() {
      //1 获取记录
      await this.getTaskHistoryList();
      //2 查看日期是否处于未发布状态
      if (!this.searchData.time) {
        return;
      }
      if (this.examTimeDisabledArray.includes(this.searchData.time)) {
        this.searchData.time = '';
        this.searchData.month = '';
        this.searchData.year = '';
        this.echartList = [];
        this.tableData = [];
      }
    },
    //点击上传/下载检测明细
    handleUploadDetectDetail(type) {
      this.uploadDetectDetailProps.modalType = type;
      this.uploadDetectDetailProps.taskInfo = this.taskInfo;
      this.uploadDetectDetailVisible = true;
    },
    changeUploadDetectDetailVisible(val) {
      this.uploadDetectDetailVisible = val;
    },
  },
  computed: {
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
    }),
  },
};
</script>
<style lang="less" scoped>
.page-assessmentresult {
  height: 100%;
  @{_deep}.head-center {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  @{_deep} .ivu-date-picker-cells-cell-focused {
    background: #2d8cf0;
    color: #fff;
  }
  .right-content {
    float: right;
    overflow-y: auto;
    width: 100%;
    height: 100%;
    background: var(--bg-content);
    @{_deep}.search-wrapper {
      //overflow: hidden;
      padding: 0 20px 20px 20px !important;
      border-bottom: 1px solid var(--border-color);
      margin: 20px 0px;
      .download-scheme {
        span {
          text-decoration: underline;
        }
      }
      .el-date-editor {
        width: 212px;
      }
      .el-icon-date {
        display: none;
      }
      .ui-label {
        line-height: 34px;
      }
      .ivu-select {
        height: 34px;
      }
      .el-input__prefix {
        left: unset;
        right: 0;
        width: 32px;
      }
      .el-input__icon,
      .ivu-input-suffix i {
        color: var(--color-primary);
        font-size: 16px;
      }
      .ivu-input-suffix,
      .ivu-input-suffix i {
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .el-input__icon {
        line-height: 34px;
      }
    }
    .jump {
      padding: 0 20px !important;
      margin-bottom: 10px;
    }
    .title-center {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      margin-bottom: 10px;
      .conter-center {
        flex: 1;
        height: 30px;
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        display: inline-block;
        vertical-align: middle;
        span {
          line-height: 30px;
          height: 100%;
          color: var(--color-content);
          display: inline-block;
        }
      }
    }
    .statistica-overview {
      height: 54%;
      margin: 0px 20px;
    }
    @{_deep}.active-blue {
      color: var(--color-display-text) !important;
      .molecular {
        &::after {
          border-color: var(--color-display-text);
        }
      }
    }
    .table-box {
      padding: 0 20px 20px 20px;
      position: relative;
      .span-btn {
        cursor: pointer;
        color: var(--color-primary);
        text-decoration: underline;
      }
      .dis-span-btn {
        color: var(--color-primary);
        text-decoration: none;
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      @{_deep} .ui-table {
        height: 738px;
      }
    }
    @{_deep}.tooltip-title {
      color: var(--color-primary);
      font-size: 14px;
      font-weight: bold;
    }
    @{_deep}.ivu-tooltip-inner {
      max-width: 640px !important;
    }
  }
  .is-show {
    width: 150px;
    height: 30px;
    line-height: 30px;
    cursor: pointer;
    color: var(--color-primary);
    font-size: 14px;
    i {
      margin-right: 5px;
    }
  }
}
</style>
